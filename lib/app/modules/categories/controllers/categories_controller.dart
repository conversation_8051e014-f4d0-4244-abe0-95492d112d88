import 'package:get/get.dart';
import 'package:pocket_trac/app/extensions/box_provider_extension.dart';
import 'package:pocket_trac/enums.dart';
import 'package:talker_flutter/talker_flutter.dart';
import '../../../models/erp_category.dart';
import '../../../repositories/category_repository.dart';
import '../../../providers/box_provider.dart';
import '../../../routes/app_pages.dart';

class CategoriesController extends GetxController with StateMixin<String> {
  final CategoryRepository categoryRepository;
  BoxProvider get boxProvider => categoryRepository.boxProvider;
  Talker get talker => categoryRepository.talker;

  Iterable<ErpCategory> get categories{
    final box = boxProvider.getBox(Boxes.categories);
    return box.getValues().map((e) => ErpCategory.fromJson(e));
  }

  CategoriesController({
    required this.categoryRepository,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change(null, status: RxStatus.loading());

      final categoryList = await categoryRepository.getAllAsync();
      final box = boxProvider.getBox(Boxes.categories);
      await box.erase();

      // If no categories exist, create sample data
      if (categoryList.isEmpty) {
        change(null, status: RxStatus.error('Failed to load categories: '));
        // change('', status: RxStatus.empty());
      } else {
        for (final category in categoryList) {
          final key = '${category.id}';
          final value = category.toJson();
          await box.write(key, value);
        }
        change('', status: RxStatus.success());
      }
    } catch (e, s) {
      talker.error('Failed to load categories: $e', e, s);
      change(null, status: RxStatus.error('Failed to load categories: $e'));
    }
  }

  void navigateToCategoryDetail(ErpCategory category) {
    Get.toNamed(Routes.CATEGORY_DETAIL, arguments: category);
  }

  void showAddCategoryDialog() {
    Get.toNamed(Routes.CATEGORY_DETAIL);
  }
}
