import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/erp_category.dart';
import '../../../utils/category_icons.dart';
import '../../../../colors.dart';
import '../controllers/categories_controller.dart';

class CategoriesView extends GetView<CategoriesController> {
  const CategoriesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ErpColors.backgroundLight,
      body: controller.obx(
        (state) => _buildContent(context, controller.categories),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onEmpty: _buildEmptyState(),
        onError: _buildErrorState,
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildErrorState(String? error) {
    Iterable<Widget> getChildren() sync* {
      yield Icon(
        Icons.error_outline,
        size: 64,
        color: ErpColors.error,
      );
      yield const SizedBox(height: 16);
      yield Text(
        '載入失敗',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Text(
        error ?? 'Unknown error',
        style: TextStyle(
          color: ErpColors.textSecondary,
        ),
        textAlign: TextAlign.center,
      );
      yield const SizedBox(height: 16);
      yield ElevatedButton(
        onPressed: controller.onRefresh,
        child: const Text('重試'),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: getChildren().toList(),
      ),
    );
  }

  Widget _buildContent(BuildContext context, Iterable<ErpCategory> it) {
    return RefreshIndicator(
      onRefresh: () async {
        controller.onRefresh();
      },
      child: ListView.separated(
        itemBuilder: (context, index) {
          final category = controller.categories.elementAt(index);
          return _buildCategoryItem(category);
        },
        separatorBuilder: (context, index) => const Divider(),
        itemCount: it.length,
      ),
    );
  }

  Widget _buildEmptyState() {
    Iterable<Widget> getChildren() sync* {
      yield Icon(
        Icons.category_outlined,
        size: 64,
        color: ErpColors.textHint,
      );
      yield const SizedBox(height: 16);
      yield Text(
        '尚無分類',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: ErpColors.textSecondary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Text(
        '點擊右下角的 + 按鈕新增第一個分類',
        style: TextStyle(
          color: ErpColors.textHint,
        ),
        textAlign: TextAlign.center,
      );
    }

    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: getChildren().toList(growable: false),
      ),
    );
  }

  Widget _buildCategoryItem(ErpCategory category) {
    final categoryName = category.name ?? '未知分類';
    final transactionCount = category.children.length;
    final amount = category.amount ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: ErpColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        shadowColor: ErpColors.shadow,
        child: InkWell(
          onTap: () => controller.navigateToCategoryDetail(category),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildCategoryIcon(categoryName),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryInfo(
                      categoryName, transactionCount, amount),
                ),
                Icon(
                  Icons.chevron_right,
                  color: ErpColors.textHint,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(String categoryName) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: CategoryIcons.getBackgroundColor(categoryName),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        CategoryIcons.getIcon(categoryName),
        color: CategoryIcons.getColor(categoryName),
        size: 20,
      ),
    );
  }

  Widget _buildCategoryInfo(
      String categoryName, int transactionCount, num amount) {
    final isIncome = amount > 0;
    final amountText = amount == 0
        ? '\$0'
        : '${isIncome ? '+' : '-'}\$${amount.abs().toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          categoryName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$transactionCount 筆交易 • 本月 $amountText',
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ErpColors.gradientStart,
            ErpColors.gradientEnd,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: ErpColors.gradientStart.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton(
        onPressed: controller.showAddCategoryDialog,
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(
          Icons.add,
          color: ErpColors.textWhite,
          size: 24,
        ),
      ),
    );
  }
}
